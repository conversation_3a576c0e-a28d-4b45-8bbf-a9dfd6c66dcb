<!--
  统一阅读器组件
  根据书籍格式动态加载对应的阅读器（TXT、PDF等）
-->

<template>
  <div class="unified-reader">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>正在加载阅读器...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-icon>
        <WarningFilled />
      </el-icon>
      <span>{{ error }}</span>
      <el-button @click="retry" type="primary" size="small">重试</el-button>
    </div>

    <!-- TXT阅读器 -->
    <EmbeddedTxtReader
      v-else-if="bookFormat === 'txt'"
      :book-id="bookId"
      @go-back="handleGoBack"
    />

    <!-- PDF阅读器 -->
    <PdfReaderView
      v-else-if="bookFormat === 'pdf'"
      :book-id="bookId"
      @go-back="handleGoBack"
    />

    <!-- EPUB阅读器 (现代化重新实现) -->
    <EpubReaderView
      v-else-if="bookFormat === 'epub'"
      :book-id="bookId"
      @go-back="handleGoBack"
    />

    <!-- 调试信息 -->
    <!-- <div v-if="!isLoading && !error" class="debug-info" style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; font-size: 12px; z-index: 9999;">
      <div>Loading: {{ isLoading }}</div>
      <div>Error: {{ error }}</div>
      <div>Format: {{ bookFormat }}</div>
      <div>BookId: {{ bookId }}</div>
    </div> -->

    <!-- MOBI阅读器（预留） -->
    <div v-else-if="bookFormat === 'mobi'" class="format-placeholder">
      <el-icon><Document /></el-icon>
      <h3>MOBI阅读器</h3>
      <p>MOBI格式支持正在开发中...</p>
      <el-button @click="handleGoBack" type="primary">返回图书列表</el-button>
    </div>

    <!-- 不支持的格式 -->
    <div v-else class="unsupported-format">
      <el-icon><WarningFilled /></el-icon>
      <h3>不支持的文件格式</h3>
      <p>当前不支持 {{ bookFormat?.toUpperCase() }} 格式的文件</p>
      <el-button @click="handleGoBack" type="primary">返回图书列表</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Loading,
  WarningFilled,
  Document
} from '@element-plus/icons-vue'
import EmbeddedTxtReader from './EmbeddedTxtReader.vue'
import PdfReaderView from './PdfReaderView.vue'
import EpubReaderView from './EpubReaderView.vue'


// Props
interface Props {
  /** 书籍ID */
  bookId: string
}

const props = defineProps<Props>()

// 路由
const router = useRouter()

// 响应式数据
const isLoading = ref(true)
const error = ref<string>('')
const bookInfo = ref<any>(null)
const bookFormat = ref<string>('')
const bookFilePath = ref<string>('')
const initialPage = ref(1)

// 计算属性
const supportedFormats = computed(() => ['txt', 'pdf', 'epub', 'mobi'])

// 方法

const loadBookInfo = async () => {
  try {
    isLoading.value = true
    error.value = ''

    console.log(`UnifiedReader: 加载书籍信息 ${props.bookId}`)

    // 增强的Electron环境预检查
    const waitForElectronReady = async (maxRetries = 100, delay = 100) => {
      console.log('UnifiedReader: 开始检查Electron环境...')

      for (let i = 0; i < maxRetries; i++) {
        // 检查是否在Electron环境中
        if (typeof window === 'undefined') {
          console.log(`UnifiedReader: 等待window对象... (${i + 1}/${maxRetries})`)
          await new Promise(resolve => setTimeout(resolve, delay))
          continue
        }

        // 检查electronAPI对象是否存在
        if (!window.electronAPI) {
          console.log(`UnifiedReader: 等待electronAPI对象... (${i + 1}/${maxRetries})`)
          await new Promise(resolve => setTimeout(resolve, delay))
          continue
        }

        console.log('UnifiedReader: Electron环境检查通过')
        return true
      }

      console.error('UnifiedReader: Electron环境检查超时')
      return false
    }

    // 增强的API可用性检查
    const waitForAPI = async (maxRetries = 50, delay = 200) => {
      console.log('UnifiedReader: 开始检查API可用性...')

      for (let i = 0; i < maxRetries; i++) {
        try {
          // 详细的API结构检查
          if (!window.electronAPI) {
            console.log(`UnifiedReader: electronAPI对象不存在 (${i + 1}/${maxRetries})`)
            await new Promise(resolve => setTimeout(resolve, delay))
            continue
          }

          if (!window.electronAPI.reader) {
            console.log(`UnifiedReader: reader API不存在 (${i + 1}/${maxRetries})`)
            await new Promise(resolve => setTimeout(resolve, delay))
            continue
          }

          if (typeof window.electronAPI.reader.getBook !== 'function') {
            console.log(`UnifiedReader: getBook方法不是函数 (${i + 1}/${maxRetries})`)
            await new Promise(resolve => setTimeout(resolve, delay))
            continue
          }

          console.log('UnifiedReader: API可用性检查通过')
          return true
        } catch (checkError) {
          console.warn(`UnifiedReader: API检查异常 (${i + 1}/${maxRetries}):`, checkError)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }

      console.error('UnifiedReader: API可用性检查超时')
      return false
    }

    // 首先检查Electron环境
    const electronReady = await waitForElectronReady()
    if (!electronReady) {
      throw new Error('Electron环境未就绪，请确保应用在Electron环境中运行')
    }

    // 然后检查API可用性
    const apiAvailable = await waitForAPI()
    if (!apiAvailable) {
      throw new Error('阅读器API初始化超时，请稍后重试或重启应用')
    }

    console.log('UnifiedReader: 开始调用阅读器API获取书籍信息')

    // 带重试机制的API调用
    let book = null
    let retryCount = 0
    const maxApiRetries = 3

    while (retryCount < maxApiRetries && !book) {
      try {
        console.log(`UnifiedReader: API调用尝试 ${retryCount + 1}/${maxApiRetries}`)
        book = await window.electronAPI.reader.getBook(props.bookId)

        if (book) {
          console.log('UnifiedReader: 成功获取书籍信息')
          break
        } else {
          console.warn(`UnifiedReader: API返回空结果 (尝试 ${retryCount + 1}/${maxApiRetries})`)
        }
      } catch (apiError) {
        retryCount++
        console.error(`UnifiedReader: API调用失败 (尝试 ${retryCount}/${maxApiRetries}):`, apiError)

        if (retryCount < maxApiRetries) {
          console.log(`UnifiedReader: 将在1秒后重试...`)
          await new Promise(resolve => setTimeout(resolve, 1000))
        } else {
          throw new Error(`API调用失败: ${apiError.message || apiError}`)
        }
      }
    }

    if (!book) {
      throw new Error('书籍不存在或无法获取书籍信息')
    }

    // 验证书籍数据完整性
    if (!book.id || !book.title) {
      throw new Error('书籍数据不完整，缺少必要字段')
    }

    bookInfo.value = book
    bookFormat.value = book.file_format || book.format || 'txt'
    bookFilePath.value = book.file_path || book.filePath || ''

    // 获取阅读进度
    if (book.current_page) {
      initialPage.value = book.current_page
    }

    console.log(`UnifiedReader: 书籍信息加载成功`, {
      id: book.id,
      title: book.title,
      format: bookFormat.value,
      filePath: bookFilePath.value,
      initialPage: initialPage.value
    })

    // 检查格式支持
    if (!supportedFormats.value.includes(bookFormat.value)) {
      console.warn(`UnifiedReader: 不支持的格式 ${bookFormat.value}`)
    }

  } catch (err) {
    console.error('UnifiedReader: 加载书籍信息失败:', err)

    // 提供更详细的错误信息
    let errorMessage = '加载失败'
    if (err.message) {
      errorMessage = err.message
    } else if (typeof err === 'string') {
      errorMessage = err
    } else {
      errorMessage = '未知错误，请查看控制台获取详细信息'
    }

    error.value = errorMessage
  } finally {
    isLoading.value = false
  }
}

// 错误诊断和恢复工具
const diagnoseEnvironment = () => {
  const diagnosis = {
    timestamp: new Date().toISOString(),
    environment: {
      isElectron: typeof window !== 'undefined' && !!window.electronAPI,
      hasWindow: typeof window !== 'undefined',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
    },
    api: {
      electronAPI: !!window.electronAPI,
      readerAPI: !!(window.electronAPI && window.electronAPI.reader),
      getBookMethod: !!(window.electronAPI && window.electronAPI.reader && typeof window.electronAPI.reader.getBook === 'function')
    },
    bookId: props.bookId,
    currentState: {
      isLoading: isLoading.value,
      hasError: !!error.value,
      errorMessage: error.value,
      hasBookInfo: !!bookInfo.value
    }
  }

  console.log('UnifiedReader: 环境诊断报告', diagnosis)
  return diagnosis
}

// 智能重试函数
const retryWithDiagnosis = async () => {
  console.log('UnifiedReader: 开始智能重试...')

  // 先进行环境诊断
  const diagnosis = diagnoseEnvironment()

  // 根据诊断结果决定重试策略
  if (!diagnosis.environment.isElectron) {
    error.value = '应用未在Electron环境中运行，请重启应用'
    return
  }

  if (!diagnosis.api.electronAPI) {
    error.value = 'Electron API未初始化，请等待应用完全加载后重试'
    return
  }

  if (!diagnosis.api.readerAPI) {
    error.value = '阅读器API不可用，请检查应用状态或重启应用'
    return
  }

  // 清除之前的错误状态
  error.value = ''

  // 延迟重试，给系统更多时间初始化
  console.log('UnifiedReader: 延迟2秒后重试...')
  await new Promise(resolve => setTimeout(resolve, 2000))

  // 重新加载书籍信息
  await loadBookInfo()
}

const retry = () => {
  retryWithDiagnosis()
}

const handleGoBack = () => {
  console.log('UnifiedReader: 返回图书列表')
  router.push('/bookshelf/library')
}



// 环境预检查函数
const performEnvironmentCheck = async () => {
  console.log('UnifiedReader: 执行环境预检查...')

  try {
    // 基础环境检查
    if (typeof window === 'undefined') {
      throw new Error('Window对象不可用')
    }

    // 等待一小段时间确保Electron完全初始化
    await new Promise(resolve => setTimeout(resolve, 500))

    // 检查Electron API
    if (!window.electronAPI) {
      console.warn('UnifiedReader: ElectronAPI暂未可用，将在加载时等待')
    } else {
      console.log('UnifiedReader: ElectronAPI已可用')

      // 检查阅读器API
      if (window.electronAPI.reader) {
        console.log('UnifiedReader: 阅读器API已可用')
      } else {
        console.warn('UnifiedReader: 阅读器API暂未可用')
      }
    }

    console.log('UnifiedReader: 环境预检查完成')
    return true
  } catch (error) {
    console.error('UnifiedReader: 环境预检查失败:', error)
    return false
  }
}

// 生命周期
onMounted(async () => {
  console.log('UnifiedReader: 组件已挂载，开始初始化...')

  // 执行环境预检查
  const envCheckPassed = await performEnvironmentCheck()

  if (!envCheckPassed) {
    console.warn('UnifiedReader: 环境预检查未通过，但仍尝试加载书籍信息')
  }

  // 加载书籍信息
  await loadBookInfo()
})

// 监听器
watch(() => props.bookId, () => {
  if (props.bookId) {
    loadBookInfo()
  }
})
</script>

<style scoped>
.unified-reader {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.loading-container,
.error-container,
.format-placeholder,
.unsupported-format {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  text-align: center;
}

.loading-container .el-icon,
.error-container .el-icon,
.format-placeholder .el-icon,
.unsupported-format .el-icon {
  font-size: 64px;
  margin-bottom: 24px;
}

.loading-container {
  color: #409eff;
}

.error-container {
  color: #f56c6c;
}

.format-placeholder,
.unsupported-format {
  color: #909399;
}

.format-placeholder h3,
.unsupported-format h3 {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 500;
}

.format-placeholder p,
.unsupported-format p {
  margin: 0 0 24px 0;
  font-size: 16px;
  line-height: 1.5;
}

.error-container .el-button {
  margin-top: 16px;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .unified-reader {
    background: #1a1a1a;
  }
  
  .format-placeholder,
  .unsupported-format {
    color: #ccc;
  }
}
</style>
