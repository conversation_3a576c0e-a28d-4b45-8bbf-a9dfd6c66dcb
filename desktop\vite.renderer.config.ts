import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  mode: process.env.NODE_ENV || 'production',
  root: resolve(__dirname, 'src/renderer'),
  envDir: resolve(__dirname),
  base: './',

  server: {
    port: 5173,
    host: '127.0.0.1'
  },

  plugins: [
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag) => tag.startsWith('webview')
        }
      }
    }),
    // 自定义插件：强制阻止Node.js模块被包含
    {
      name: 'block-node-modules',
      resolveId(id, importer) {
        // 定义需要阻止的模块列表
        const blockedModules = [
          'fs', 'path', 'os', 'crypto', 'util', 'events', 'buffer', 'stream',
          'http', 'https', 'url', 'querystring', 'zlib', 'child_process',
          'canvas', '@napi-rs/canvas', 'node-canvas'
        ]

        // 如果是被阻止的模块，返回虚拟模块
        if (blockedModules.includes(id)) {
          return '\0virtual:empty-module'
        }

        // 排除scripts目录
        if (id.includes('/scripts/') || id.includes('\\scripts\\')) {
          return { id, external: true }
        }

        return null
      },
      load(id) {
        // 为虚拟模块提供空内容
        if (id === '\0virtual:empty-module') {
          return 'export default {}; export const __esModule = true;'
        }
        return null
      }
    }
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@renderer': resolve(__dirname, 'src/renderer'),
      '@shared': resolve(__dirname, 'src/shared'),
      // 重定向Node.js模块到空模块，防止被包含到渲染进程
      'fs': resolve(__dirname, 'src/renderer/utils/empty-module.js'),
      'path': resolve(__dirname, 'src/renderer/utils/empty-module.js'),
      'os': resolve(__dirname, 'src/renderer/utils/empty-module.js'),
      'crypto': resolve(__dirname, 'src/renderer/utils/empty-module.js'),
      'util': resolve(__dirname, 'src/renderer/utils/empty-module.js'),
      'events': resolve(__dirname, 'src/renderer/utils/empty-module.js'),
      'buffer': resolve(__dirname, 'src/renderer/utils/empty-module.js'),
      'stream': resolve(__dirname, 'src/renderer/utils/empty-module.js'),
      'canvas': resolve(__dirname, 'src/renderer/utils/empty-module.js'),
      '@napi-rs/canvas': resolve(__dirname, 'src/renderer/utils/empty-module.js')
    }
  },
  define: {
    __VUE_OPTIONS_API__: false,
    __VUE_PROD_DEVTOOLS__: process.env.NODE_ENV === 'development',
    // 定义全局变量来替换Node.js模块引用
    global: 'globalThis',
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production')
  },
  optimizeDeps: {
    include: ['vue', 'vue-router', 'pinia', 'element-plus'],
    exclude: [
      'iconv-lite',
      'better-sqlite3',
      'canvas',
      '@napi-rs/canvas',
      'electron-pdf-window',
      'node-ensure',
      'fs',
      'path',
      'os',
      'stream',
      'crypto',
      'util',
      'events',
      'buffer'
    ]
  },
  build: {
    outDir: resolve(__dirname, 'dist/renderer'),
    emptyOutDir: true,
    minify: process.env.NODE_ENV === 'production',
    sourcemap: process.env.NODE_ENV === 'development',
    rollupOptions: {
      input: resolve(__dirname, 'src/renderer/index.html'),
      external: (id) => {
        // 强制外部化所有Node.js内置模块
        const nodeModules = [
          'fs', 'path', 'os', 'crypto', 'util', 'events', 'buffer', 'stream',
          'http', 'https', 'url', 'querystring', 'zlib', 'child_process',
          'cluster', 'dgram', 'dns', 'net', 'readline', 'repl', 'tls', 'tty',
          'vm', 'worker_threads', 'perf_hooks', 'async_hooks', 'inspector'
        ]

        // 强制外部化canvas相关模块
        const canvasModules = ['canvas', '@napi-rs/canvas', 'node-canvas']

        // 强制外部化其他Node.js专用模块
        const electronModules = [
          'iconv-lite', 'better-sqlite3', 'electron-pdf-window', 'node-ensure'
        ]

        // 检查是否是需要外部化的模块
        if (nodeModules.includes(id) ||
            canvasModules.some(mod => id.includes(mod)) ||
            electronModules.includes(id)) {
          return true
        }

        return false
      },
      output: {
        // 确保PDF.js worker文件被正确复制
        assetFileNames: (assetInfo) => {
          if (assetInfo.name && assetInfo.name.includes('pdf.worker')) {
            return 'assets/[name][extname]'
          }
          return 'assets/[name]-[hash][extname]'
        }
      }
    }
  },
  worker: {
    format: 'es'
  }
})
