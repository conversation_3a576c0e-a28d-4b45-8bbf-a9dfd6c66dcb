/**
 * IPC通信处理器
 * 负责处理主进程和渲染进程之间的通信
 */

import { ipcMain } from 'electron'
import { DatabaseManager } from './database-manager'
import { FileManager } from './file-manager'
import { WindowManager } from './window-manager'
import type { BookInfo, Bookmark, Note, Settings } from '@shared/types'
import { randomUUID } from 'crypto'

export class IPCHandler {
  constructor(
    private databaseManager: DatabaseManager,
    private fileManager: FileManager,
    private windowManager: WindowManager
  ) {}

  /**
   * 注册所有IPC处理器
   */
  registerHandlers(): void {
    this.registerBookHandlers()
    this.registerBookmarkHandlers()
    this.registerNoteHandlers()
    this.registerSettingsHandlers()
    this.registerFileHandlers()
    this.registerWindowHandlers()
    this.registerThemeHandlers()
  }

  /**
   * 注册书籍相关的IPC处理器
   */
  private registerBookHandlers(): void {
    const db = this.databaseManager.getDatabase()

    // 添加书籍
    ipcMain.handle('book:add', async (_, filePath: string): Promise<BookInfo> => {
      try {
        const fileInfo = await this.fileManager.getFileInfo(filePath)
        const now = new Date().toISOString()

        const book: BookInfo = {
          id: randomUUID(),
          title: fileInfo.name,
          author: '未知作者', // TODO: 从文件中提取作者信息
          filePath,
          fileSize: fileInfo.size,
          format: fileInfo.format as any,
          addedAt: new Date(),
          readProgress: 0
        }

        const stmt = db.prepare(`
          INSERT INTO books (
            title, author, file_path, file_size, file_format,
            reading_progress_percent, import_time, created_at, updated_at,
            language, reading_status, current_page, total_pages, word_count
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `)

        const result = stmt.run(
          book.title,
          book.author,
          book.filePath,
          book.fileSize,
          book.format,
          book.readProgress,
          now,
          now,
          now,
          'zh-CN',
          'unread',
          0,
          0,
          0
        )

        // 获取插入的ID并更新book对象
        book.id = result.lastInsertRowid.toString()
        console.log('成功添加书籍:', book.title, 'ID:', book.id)
        return book
      } catch (error) {
        console.error('添加书籍失败:', error)
        throw error
      }
    })

    // 删除书籍
    ipcMain.handle('book:remove', async (_, bookId: string): Promise<boolean> => {
      try {
        const stmt = db.prepare('DELETE FROM books WHERE id = ?')
        const result = stmt.run(bookId)
        return result.changes > 0
      } catch (error) {
        console.error('删除书籍失败:', error)
        throw error
      }
    })

    // 获取书籍列表
    ipcMain.handle('book:list', async (): Promise<BookInfo[]> => {
      try {
        const stmt = db.prepare(`
          SELECT
            id,
            title,
            author,
            file_path as filePath,
            file_size as fileSize,
            file_format as format,
            reading_progress_percent as readProgress,
            current_page as currentPage,
            import_time as addedAt,
            last_read_time as lastReadAt,
            cover_image as coverPath
          FROM books
          WHERE deleted_at IS NULL
          ORDER BY last_read_time DESC, import_time DESC
        `)
        const rows = stmt.all() as any[]

        console.log(`获取到 ${rows.length} 本书籍`)

        return rows.map(row => ({
          id: row.id.toString(),
          title: row.title || '未知标题',
          author: row.author || '未知作者',
          filePath: row.filePath,
          fileSize: row.fileSize || 0,
          format: row.format || 'txt',
          readProgress: row.readProgress || 0,
          currentPage: row.currentPage || 0,
          addedAt: row.addedAt ? new Date(row.addedAt) : new Date(),
          lastReadAt: row.lastReadAt ? new Date(row.lastReadAt) : undefined,
          coverPath: row.coverPath
        }))
      } catch (error) {
        console.error('获取书籍列表失败:', error)
        throw error
      }
    })

    // 获取单本书籍
    ipcMain.handle('book:get', async (_, bookId: string): Promise<BookInfo | null> => {
      try {
        const stmt = db.prepare(`
          SELECT
            id,
            title,
            author,
            file_path as filePath,
            file_size as fileSize,
            file_format as format,
            reading_progress_percent as readProgress,
            current_page as currentPage,
            import_time as addedAt,
            last_read_time as lastReadAt,
            cover_image as coverPath
          FROM books
          WHERE id = ? AND deleted_at IS NULL
        `)
        const row = stmt.get(bookId) as any

        if (!row) return null

        return {
          id: row.id.toString(),
          title: row.title || '未知标题',
          author: row.author || '未知作者',
          filePath: row.filePath,
          fileSize: row.fileSize || 0,
          format: row.format || 'txt',
          readProgress: row.readProgress || 0,
          currentPage: row.currentPage || 0,
          addedAt: row.addedAt ? new Date(row.addedAt) : new Date(),
          lastReadAt: row.lastReadAt ? new Date(row.lastReadAt) : undefined,
          coverPath: row.coverPath
        }
      } catch (error) {
        console.error('获取书籍失败:', error)
        throw error
      }
    })

    // 更新阅读进度
    ipcMain.handle('book:update-progress', async (
      _,
      bookId: string,
      progress: number,
      currentPage?: number
    ): Promise<boolean> => {
      try {
        const stmt = db.prepare(`
          UPDATE books
          SET
            reading_progress_percent = ?,
            current_page = ?,
            last_read_time = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP,
            reading_status = CASE
              WHEN ? >= 100 THEN 'finished'
              WHEN ? > 0 THEN 'reading'
              ELSE 'unread'
            END
          WHERE id = ? AND deleted_at IS NULL
        `)
        const result = stmt.run(progress, currentPage || 0, progress, progress, bookId)
        console.log(`更新阅读进度: 书籍ID=${bookId}, 进度=${progress}%, 页码=${currentPage}`)
        return result.changes > 0
      } catch (error) {
        console.error('更新阅读进度失败:', error)
        throw error
      }
    })
  }

  /**
   * 注册书签相关的IPC处理器
   */
  private registerBookmarkHandlers(): void {
    const db = this.databaseManager.getDatabase()

    // 添加书签
    ipcMain.handle('bookmark:add', async (_, bookmark: Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt'>): Promise<Bookmark> => {
      try {
        const bookmarkId = randomUUID()
        const now = new Date()
        
        const newBookmark: Bookmark = {
          ...bookmark,
          id: bookmarkId,
          createdAt: now,
          updatedAt: now
        }

        const stmt = db.prepare(`
          INSERT INTO bookmarks (id, bookId, title, content, position, createdAt, updatedAt)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `)
        
        stmt.run(
          newBookmark.id,
          newBookmark.bookId,
          newBookmark.title,
          newBookmark.content,
          newBookmark.position,
          newBookmark.createdAt.toISOString(),
          newBookmark.updatedAt.toISOString()
        )

        return newBookmark
      } catch (error) {
        console.error('添加书签失败:', error)
        throw error
      }
    })

    // 删除书签
    ipcMain.handle('bookmark:remove', async (_, bookmarkId: string): Promise<boolean> => {
      try {
        const stmt = db.prepare('DELETE FROM bookmarks WHERE id = ?')
        const result = stmt.run(bookmarkId)
        return result.changes > 0
      } catch (error) {
        console.error('删除书签失败:', error)
        throw error
      }
    })

    // 获取书签列表
    ipcMain.handle('bookmark:list', async (_, bookId: string): Promise<Bookmark[]> => {
      try {
        const stmt = db.prepare(`
          SELECT * FROM bookmarks 
          WHERE bookId = ? 
          ORDER BY createdAt DESC
        `)
        const rows = stmt.all(bookId) as any[]
        
        return rows.map(row => ({
          ...row,
          createdAt: new Date(row.createdAt),
          updatedAt: new Date(row.updatedAt)
        }))
      } catch (error) {
        console.error('获取书签列表失败:', error)
        throw error
      }
    })
  }

  /**
   * 注册笔记相关的IPC处理器
   */
  private registerNoteHandlers(): void {
    const db = this.databaseManager.getDatabase()

    // 添加笔记
    ipcMain.handle('note:add', async (_, note: Omit<Note, 'id' | 'createdAt' | 'updatedAt'>): Promise<Note> => {
      try {
        const noteId = randomUUID()
        const now = new Date()
        
        const newNote: Note = {
          ...note,
          id: noteId,
          createdAt: now,
          updatedAt: now
        }

        const stmt = db.prepare(`
          INSERT INTO notes (id, bookId, title, content, position, color, createdAt, updatedAt)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `)
        
        stmt.run(
          newNote.id,
          newNote.bookId,
          newNote.title,
          newNote.content,
          newNote.position,
          newNote.color,
          newNote.createdAt.toISOString(),
          newNote.updatedAt.toISOString()
        )

        return newNote
      } catch (error) {
        console.error('添加笔记失败:', error)
        throw error
      }
    })

    // 获取笔记列表
    ipcMain.handle('note:list', async (_, bookId: string): Promise<Note[]> => {
      try {
        const stmt = db.prepare(`
          SELECT * FROM notes 
          WHERE bookId = ? 
          ORDER BY createdAt DESC
        `)
        const rows = stmt.all(bookId) as any[]
        
        return rows.map(row => ({
          ...row,
          createdAt: new Date(row.createdAt),
          updatedAt: new Date(row.updatedAt)
        }))
      } catch (error) {
        console.error('获取笔记列表失败:', error)
        throw error
      }
    })
  }

  /**
   * 注册设置相关的IPC处理器
   */
  private registerSettingsHandlers(): void {
    const db = this.databaseManager.getDatabase()

    // 获取设置
    ipcMain.handle('settings:get', async (): Promise<Settings> => {
      try {
        const stmt = db.prepare('SELECT key, value FROM settings')
        const rows = stmt.all() as { key: string; value: string }[]
        
        const settings: any = {}
        for (const row of rows) {
          settings[row.key] = JSON.parse(row.value)
        }
        
        return settings as Settings
      } catch (error) {
        console.error('获取设置失败:', error)
        throw error
      }
    })

    // 更新设置
    ipcMain.handle('settings:update', async (_, updates: Partial<Settings>): Promise<boolean> => {
      try {
        const stmt = db.prepare('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)')
        
        for (const [key, value] of Object.entries(updates)) {
          stmt.run(key, JSON.stringify(value))
        }
        
        return true
      } catch (error) {
        console.error('更新设置失败:', error)
        throw error
      }
    })
  }

  /**
   * 注册文件相关的IPC处理器
   */
  private registerFileHandlers(): void {
    // 读取文件
    ipcMain.handle('file:read', async (_, filePath: string): Promise<Buffer> => {
      return this.fileManager.readFile(filePath)
    })

    // 检查文件是否存在
    ipcMain.handle('file:exists', async (_, filePath: string): Promise<boolean> => {
      return this.fileManager.fileExists(filePath)
    })

    // 选择文件
    ipcMain.handle('file:select', async (_, filters?: Electron.FileFilter[]): Promise<string[]> => {
      return this.fileManager.selectFiles(filters)
    })

    // 写入文件
    ipcMain.handle('file:write', async (_, filePath: string, data: Buffer): Promise<void> => {
      return this.fileManager.writeFile(filePath, data)
    })
  }

  /**
   * 注册窗口相关的IPC处理器
   */
  private registerWindowHandlers(): void {
    // 最小化窗口
    ipcMain.handle('window:minimize', () => {
      this.windowManager.minimizeWindow()
    })

    // 最大化窗口
    ipcMain.handle('window:maximize', () => {
      this.windowManager.toggleMaximizeWindow()
    })

    // 关闭窗口
    ipcMain.handle('window:close', () => {
      this.windowManager.closeWindow()
    })

    // 切换全屏
    ipcMain.handle('window:toggle-fullscreen', () => {
      this.windowManager.toggleFullscreen()
    })
  }

  /**
   * 注册主题相关的IPC处理器
   */
  private registerThemeHandlers(): void {
    // 设置主题
    ipcMain.handle('theme:set', async (_, themeId: string): Promise<void> => {
      try {
        console.log(`IPC: 设置主题为 ${themeId}`)
        // 这里可以保存主题设置到数据库或配置文件
        // 暂时只是记录日志，实际的主题应用由渲染进程处理
        return Promise.resolve()
      } catch (error) {
        console.error('IPC: 设置主题失败:', error)
        throw error
      }
    })

    // 获取当前主题
    ipcMain.handle('theme:get', async (): Promise<string> => {
      try {
        // 这里可以从数据库或配置文件读取主题设置
        // 暂时返回默认主题
        return 'light'
      } catch (error) {
        console.error('IPC: 获取主题失败:', error)
        throw error
      }
    })

    // 获取可用主题列表
    ipcMain.handle('theme:list', async (): Promise<string[]> => {
      try {
        return [
          'light',
          'dark',
          'eye-care',
          'eye-care-warm',
          'high-contrast',
          'night',
          'natural',
          'auto'
        ]
      } catch (error) {
        console.error('IPC: 获取主题列表失败:', error)
        throw error
      }
    })
  }
}
